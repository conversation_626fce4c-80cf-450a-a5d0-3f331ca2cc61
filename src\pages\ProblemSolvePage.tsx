import { useState } from "react";
import { useParams } from "react-router-dom";
import Header from "@/components/Header";
import CodeEditor from "@/components/CodeEditor";
import ProblemDescription from "@/components/ProblemDescription";
import TestResults from "@/components/TestResults";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Play, Send, Settings, ChevronLeft, ChevronRight } from "lucide-react";
import { toast } from "sonner";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { problemsAPI } from "@/api/problems";
import { useEffect } from "react";
import {
  DropdownMenu,
  DropdownMenuTrigger,
} from "@radix-ui/react-dropdown-menu";
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { CodeFormatter } from "@/utils/codeFormatter";
import { useProblem } from "@/hooks/useProblems";

// Utility function to parse test cases from problem data
const parseTestCases = (testCasesString: string) => {
  try {
    if (!testCasesString) return [];
    const parsed = JSON.parse(testCasesString);
    // Handle both array format and object format
    if (Array.isArray(parsed)) {
      return parsed;
    } else if (typeof parsed === "object") {
      return Object.values(parsed);
    }
    return [];
  } catch (error) {
    console.error("Error parsing test cases:", error);
    return [];
  }
};

const langMap = {
  CPP: "cpp",
  JAVA: "java",
  PYTHON: "python",
  JAVASCRIPT: "javascript",
};

const ProblemSolvePage = () => {
  const { id } = useParams();
  const [code, setCode] = useState("");
  const [language, setLanguage] = useState("PYTHON");
  const [testResults, setTestResults] = useState(null);
  const [isRunning, setIsRunning] = useState(false);

  const { getProblemById } = problemsAPI;
  const { data: problem, isLoading: isProblemLoading } = useProblem(id);

  // Get actual test cases from problem data
  const getTestCasesFromProblem = () => {
    if (!problem) return [];

    // Try to get public test cases first, fallback to examples
    let testCases = [];

    if (problem.publicTestcases) {
      testCases = parseTestCases(problem.publicTestcases);
    }

    // If no public test cases, use examples as test cases
    if (testCases.length === 0 && problem.examples) {
      const examples =
        typeof problem.examples === "string"
          ? JSON.parse(problem.examples)
          : problem.examples;

      if (Array.isArray(examples)) {
        testCases = examples;
      } else if (typeof examples === "object") {
        testCases = Object.values(examples);
      }
    }

    return testCases;
  };

  useEffect(() => {
    getProblemById(id);
  }, [getProblemById, id]);

  useEffect(() => {
    if (problem && problem.codeSnippets) {
      setCode(problem.codeSnippets[language]);
    }
  }, [problem, language]);

  const handleRunCode = async () => {
    setIsRunning(true);
    toast("Running code...");

    // Get actual test cases from problem data
    const actualTestCases = getTestCasesFromProblem();
    console.log("Actual test cases found:", actualTestCases);

    // Simulate API call with actual test cases
    setTimeout(() => {
      if (actualTestCases.length === 0) {
        toast.error("No test cases available for this problem");
        setIsRunning(false);
        return;
      }

      // Convert actual test cases to the format expected by TestResults component
      const formattedCases = actualTestCases.map((testCase, index) => {
        // Simulate random results for demonstration
        const passed = Math.random() > 0.3; // 70% pass rate for demo
        const time = `${Math.floor(Math.random() * 50) + 1}ms`;

        // Handle different possible field names for input and output
        const input =
          testCase.input || testCase.stdin || `Test case ${index + 1} input`;
        const expected =
          testCase.output ||
          testCase.expectedOutput ||
          testCase.stdout ||
          `Expected output ${index + 1}`;

        return {
          input,
          expected,
          actual: passed ? expected : "Wrong Answer",
          passed,
          time,
        };
      });

      const passedCount = formattedCases.filter((tc) => tc.passed).length;

      setTestResults({
        passed: passedCount,
        total: formattedCases.length,
        cases: formattedCases,
      });

      setIsRunning(false);
      toast("Code execution completed");
    }, 2000);
  };

  const handleSubmit = async () => {
    setIsRunning(true);
    toast("Submitting solution...");

    // Simulate submission
    setTimeout(() => {
      toast.success("Solution accepted! +15 XP");
      setIsRunning(false);
    }, 3000);
  };

  if (isProblemLoading) return <div>Loading...</div>;

  const handleFormat = () => {
    const formattedCode = CodeFormatter.formatForJudge0(code, language);
    setCode(formattedCode);
  };

  return (
    <div className="min-h-screen bg-craft-bg">
      <Header />
      <div className="flex h-[calc(100vh-80px)] container mx-auto px-6">
        <ResizablePanelGroup direction="horizontal">
          {/* Left Panel - Problem Description */}
          <ResizablePanel>
            <div className="bg-craft-panel border-r border-craft-border overflow-y-auto [scrollbar-gutter:stable]">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <h1 className="text-xl font-bold text-craft-text-primary">
                      {problem?.title}
                    </h1>
                    <Badge className="bg-craft-success/20 text-craft-success border-craft-success/30">
                      {problem?.difficulty}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      variant="ghost"
                      className="text-craft-text-secondary hover:text-craft-accent"
                    >
                      <ChevronLeft className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="text-craft-text-secondary hover:text-craft-accent"
                    >
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                <ProblemDescription problem={problem} />
              </div>
            </div>
          </ResizablePanel>

          {/* Resize Handle */}
          <ResizableHandle />

          {/* Right Panel - Code Editor */}
          <ResizablePanel>
            <ResizablePanelGroup direction="vertical">
              <ResizablePanel>
                <div className="flex flex-col h-full">
                  {/* Editor Header */}
                  <div className="bg-craft-panel border-b border-craft-border p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <select
                          value={language}
                          onChange={(e) => setLanguage(e.target.value)}
                          className="bg-craft-bg border px-2 border-craft-border rounded py-1 text-craft-text-primary focus:border-craft-accent"
                        >
                          <option value="PYTHON">Python</option>
                          <option value="JAVASCRIPT">Javascript</option>
                          <option value="JAVA">Java</option>
                          <option value="CPP">C++</option>
                        </select>
                        <DropdownMenu>
                          <DropdownMenuTrigger>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="text-craft-text-secondary hover:text-craft-accent"
                            >
                              <Settings className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent className="bg-craft-bg border-craft-border text-craft-text-primary hover:bg-craft-bg/80">
                            <DropdownMenuLabel>Settings</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={handleFormat}>
                              Format
                            </DropdownMenuItem>
                            <DropdownMenuItem>Reset</DropdownMenuItem>
                            <DropdownMenuItem>FullScreen</DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Button
                          onClick={handleRunCode}
                          disabled={isRunning}
                          variant="outline"
                          className="border-craft-border text-black hover:border-craft-bg"
                        >
                          <Play className="w-4 h-4 mr-2" />
                          Run
                        </Button>
                        <Button
                          onClick={handleSubmit}
                          disabled={isRunning}
                          className="bg-craft-accent hover:bg-craft-accent/80 text-craft-bg"
                        >
                          <Send className="w-4 h-4 mr-2" />
                          Submit
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Code Editor */}
                  <div className="h-full">
                    <CodeEditor
                      value={code}
                      onChange={(value) => setCode(value)}
                      language={langMap[language]}
                    />
                  </div>

                  {/* Test Results */}
                </div>
              </ResizablePanel>
              <ResizableHandle />
              {testResults ? (
                <ResizablePanel>
                  <TestResults results={testResults} />
                </ResizablePanel>
              ) : (
                <ResizablePanel>
                  <div className="bg-craft-panel border-t border-craft-border p-4 h-full flex items-center justify-center">
                    <div className="text-craft-text-secondary text-center">
                      <p className="mb-2">Run your code to see test results</p>
                      <p className="text-sm">
                        Click the "Run" button to execute your code against the
                        test cases
                      </p>
                    </div>
                  </div>
                </ResizablePanel>
              )}
            </ResizablePanelGroup>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </div>
  );
};

export default ProblemSolvePage;
